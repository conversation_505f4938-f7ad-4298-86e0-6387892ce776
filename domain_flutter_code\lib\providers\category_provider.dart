import 'package:flutter/material.dart';
import '../models/category.dart';
import '../services/api_service.dart';

class CategoryProvider with ChangeNotifier {
  List<Category> _categories = [];
  bool _isLoading = false;
  String? _error;

  List<Category> get categories => _categories;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> loadCategories() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await ApiService.getCategories();
      
      if (response['success'] == true) {
        final List<dynamic> categoriesData = response['data'];
        _categories = categoriesData.map((json) => Category.fromJson(json)).toList();
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> createCategory(Map<String, dynamic> data) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await ApiService.createCategory(data);
      
      if (response['success'] == true) {
        // Don't refresh here - let the calling screen handle the refresh
        return true;
      }
      return false;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateCategory(int id, Map<String, dynamic> data) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await ApiService.updateCategory(id, data);
      
      if (response['success'] == true) {
        // Don't refresh here - let the calling screen handle the refresh
        return true;
      }
      return false;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> deleteCategory(int id) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await ApiService.deleteCategory(id);
      
      if (response['success'] == true) {
        _categories.removeWhere((category) => category.id == id);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Category? getCategoryById(int id) {
    try {
      return _categories.firstWhere((category) => category.id == id);
    } catch (e) {
      return null;
    }
  }

  List<Category> getCategoriesByIds(List<int> ids) {
    return _categories.where((category) => ids.contains(category.id)).toList();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }
}