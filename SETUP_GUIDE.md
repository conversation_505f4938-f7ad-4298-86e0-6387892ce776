# Domain Manager Flutter App Setup Guide

## Prerequisites
- PHP 8.1+
- Composer
- Node.js & npm
- Flutter SDK
- Android Studio / Xcode (for mobile development)

## Backend Setup (Laravel)

### 1. Install Laravel Sanctum
```bash
composer require laravel/sanctum
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"
php artisan migrate
```

### 2. Configure Sanctum
Add to `config/sanctum.php`:
```php
'stateful' => explode(',', env('SANCTUM_STATEFUL_DOMAINS', sprintf(
    '%s%s',
    'localhost,localhost:3000,127.0.0.1,127.0.0.1:8000,::1',
    Sanctum::currentApplicationUrlWithPort()
))),
```

### 3. Update Kernel.php
Add to `app/Http/Kernel.php` in the `api` middleware group:
```php
\Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
```

### 4. Create Admin User
```bash
php artisan tinker
```
```php
App\Models\Admin::create([
    'name' => 'Admin User',
    'email' => '<EMAIL>',
    'password' => bcrypt('password'),
    'role' => 'super_admin',
    'is_active' => true
]);
```

### 5. Start Laravel Server
```bash
php artisan serve
```

## Flutter Setup

### 1. Install Dependencies
```bash
cd domain_flutter_code
flutter pub get
```

### 2. Generate Model Files (if needed)
```bash
flutter pub run build_runner build
```

### 3. Configure Network (Android)
Add to `android/app/src/main/AndroidManifest.xml`:
```xml
<uses-permission android:name="android.permission.INTERNET" />
<application
    android:usesCleartextTraffic="true"
    ...>
```

### 4. Run Flutter App
```bash
flutter run
```

## API Endpoints

### Authentication
- POST `/api/auth/login` - Login
- POST `/api/auth/logout` - Logout
- GET `/api/auth/user` - Get current user

### Domains
- GET `/api/domains` - List domains
- POST `/api/domains` - Create domain
- PUT `/api/domains/{id}` - Update domain
- DELETE `/api/domains/{id}` - Delete domain

### Simple Domains
- GET `/api/simple-domains` - List simple domains
- POST `/api/simple-domains` - Create simple domain
- POST `/api/simple-domains/{id}/buy` - Purchase simple domain

### Categories
- GET `/api/categories` - List categories
- POST `/api/categories` - Create category

### Dashboard
- GET `/api/dashboard/stats` - Get dashboard statistics

## Default Login Credentials
- Email: <EMAIL>
- Password: password

## Troubleshooting

### Windows Development Mode
If you get symlink errors on Windows:
1. Open Settings (Win + I)
2. Go to Update & Security > For developers
3. Enable "Developer Mode"
4. Restart your computer

### Network Issues
- For Android Emulator: Use `http://********:8000/api`
- For iOS Simulator: Use `http://localhost:8000/api`
- For Physical Device: Use your computer's IP address

### CORS Issues
Add to Laravel's `config/cors.php`:
```php
'paths' => ['api/*', 'sanctum/csrf-cookie'],
'allowed_methods' => ['*'],
'allowed_origins' => ['*'],
'allowed_origins_patterns' => [],
'allowed_headers' => ['*'],
'exposed_headers' => [],
'max_age' => 0,
'supports_credentials' => false,
```