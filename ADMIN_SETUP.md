# Admin Panel Setup Guide

## Overview
This guide will help you set up the complete admin authentication system for your Domain CRM application.

## Features Included
- ✅ Admin Login Page with beautiful UI
- ✅ Admin Dashboard with system overview
- ✅ Profile Settings (name, email, password change)
- ✅ System Settings (app configuration)
- ✅ Secure authentication with separate admin guard
- ✅ Mobile-responsive admin panel
- ✅ Role-based access (admin, super_admin)

## Setup Instructions

### 1. Run Database Migrations
```bash
php artisan migrate
```

### 2. Seed Admin Users
```bash
php artisan db:seed --class=AdminSeeder
```

### 3. Default Admin Credentials
After seeding, you can login with these credentials:

**Super Admin:**
- Email: `<EMAIL>`
- Password: `admin123`

**Regular Admin:**
- Email: `<EMAIL>`
- Password: `admin123`

## Access URLs

### Admin Login
```
http://127.0.0.1:8000/admin/login
```

### Admin Dashboard (after login)
```
http://127.0.0.1:8000/admin/dashboard
```

## File Structure Created

### Controllers
- `app/Http/Controllers/Auth/AdminLoginController.php` - Handles admin authentication
- `app/Http/Controllers/AdminController.php` - Admin dashboard and settings

### Models
- `app/Models/Admin.php` - Admin user model

### Views
- `resources/views/auth/admin-login.blade.php` - Beautiful admin login page
- `resources/views/layouts/admin.blade.php` - Admin panel layout
- `resources/views/admin/dashboard.blade.php` - Admin dashboard
- `resources/views/admin/settings.blade.php` - Profile settings
- `resources/views/admin/system-settings.blade.php` - System configuration

### Database
- `database/migrations/2024_01_01_000001_create_admins_table.php` - Admin users table
- `database/seeders/AdminSeeder.php` - Default admin users

### Configuration
- Updated `config/auth.php` with admin guard and provider

## Features

### Admin Login Page
- Modern gradient design
- Responsive layout
- Password visibility toggle
- Form validation
- Demo credentials display

### Admin Dashboard
- System statistics
- Quick actions
- Recent domains overview
- System information
- Admin profile card

### Profile Settings
- Update name and email
- Change password with current password verification
- Account information display
- Password visibility toggles

### System Settings
- Application configuration
- Email settings overview
- Maintenance tools (backup, cache clearing)
- System status monitoring
- Version information

## Security Features
- Separate authentication guard for admins
- Password hashing
- CSRF protection
- Session management
- Role-based access control

## Customization

### Adding New Admin Users
You can create new admin users programmatically:

```php
use App\Models\Admin;
use Illuminate\Support\Facades\Hash;

Admin::create([
    'name' => 'New Admin',
    'email' => '<EMAIL>',
    'password' => Hash::make('secure_password'),
    'role' => 'admin', // or 'super_admin'
    'is_active' => true,
]);
```

### Customizing the Admin Panel
- Modify `resources/views/layouts/admin.blade.php` for layout changes
- Update CSS variables in the layout for color scheme changes
- Add new menu items in the sidebar navigation

## Troubleshooting

### If you get authentication errors:
1. Clear application cache: `php artisan cache:clear`
2. Clear config cache: `php artisan config:clear`
3. Ensure migrations are run: `php artisan migrate:status`

### If admin login doesn't work:
1. Verify admin users exist: Check `admins` table in database
2. Ensure correct credentials are being used
3. Check Laravel logs: `storage/logs/laravel.log`

## Next Steps
1. Access the admin login page
2. Login with provided credentials
3. Explore the admin dashboard
4. Configure system settings as needed
5. Create additional admin users if required

The admin panel is now fully functional and ready to use!