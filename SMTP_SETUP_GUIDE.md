# Gmail SMTP & Domain Expiry Alert Setup Guide

## Overview
This system provides automated domain expiry alerts via Gmail SMTP with configurable alert days and admin email settings.

## Features
- ✅ Gmail SMTP Configuration
- ✅ Configurable Alert Days (e.g., 10,20,30 days before expiry)
- ✅ Admin Email Configuration
- ✅ Automated Daily Expiry Checks
- ✅ Test Email Functionality
- ✅ Beautiful HTML Email Templates

## Gmail SMTP Setup Instructions

### Step 1: Enable 2-Step Verification
1. Go to your Google Account settings
2. Navigate to **Security** > **2-Step Verification**
3. Enable 2-Step Verification if not already enabled

### Step 2: Generate App Password
1. In Google Account settings, go to **Security**
2. Under **2-Step Verification**, click **App passwords**
3. Select **Mail** as the app
4. Generate a new App Password
5. **Important**: Copy and save this password - you'll need it for SMTP configuration

### Step 3: Configure SMTP Settings
1. Access the admin panel: `/admin/expiry-settings`
2. Fill in the SMTP configuration:
   - **SMTP Host**: `smtp.gmail.com`
   - **SMTP Port**: `587`
   - **Gmail Address**: Your Gmail address (e.g., `<EMAIL>`)
   - **Gmail App Password**: The App Password generated in Step 2
   - **Encryption**: `TLS` (recommended)

### Step 4: Configure Alert Settings
1. **Admin Email**: Email address where alerts will be sent
2. **Alert Days**: Comma-separated days before expiry (e.g., `10,20,30`)
3. **Enable Expiry Alerts**: Toggle to activate/deactivate alerts

## Environment Configuration

Update your `.env` file with Gmail SMTP settings:

```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Domain CRM System"
```

## Automated Scheduling

The system automatically checks for expiring domains daily at 8:00 AM. The schedule is configured in `app/Console/Kernel.php`:

```php
$schedule->command('app:check-domain-expiry')->dailyAt('08:00');
```

### Manual Execution
You can manually run the expiry check command:
```bash
php artisan app:check-domain-expiry
```

## Database Setup

Run the migrations to create the expiry settings table:
```bash
php artisan migrate
php artisan db:seed --class=ExpirySettingsSeeder
```

## Testing the System

### Test Email Functionality
1. Go to `/admin/expiry-settings`
2. Configure your SMTP settings
3. Click **Send Test Email** button
4. Check your admin email for the test alert

### Manual Command Test
```bash
php artisan app:check-domain-expiry
```

## Email Template Features

The email alerts include:
- Professional HTML design
- Domain details table with:
  - Domain name
  - Expiry date
  - Days remaining
  - Categories
  - Rating
- Color-coded urgency levels:
  - **Critical**: 7 days or less (red)
  - **Warning**: 8-30 days (yellow)
  - **Attention**: 31-90 days (orange)

## Troubleshooting

### Common Issues

1. **"Authentication failed" error**
   - Ensure you're using the App Password, not your regular Gmail password
   - Verify 2-Step Verification is enabled

2. **"Connection refused" error**
   - Check SMTP host and port settings
   - Ensure TLS encryption is selected

3. **No emails received**
   - Check spam/junk folder
   - Verify admin email address is correct
   - Ensure expiry alerts are enabled

### Debug Commands
```bash
# Check if domains are expiring
php artisan tinker
>>> App\Models\Domain::expiringSoon(30)->get();

# Test mail configuration
>>> Mail::raw('Test email', function($msg) { $msg->to('<EMAIL>')->subject('Test'); });
```

## Security Notes

- App passwords are more secure than regular passwords
- Store sensitive credentials in `.env` file (never commit to version control)
- Regularly rotate App passwords
- Use environment-specific email addresses for different deployments

## Support

For issues or questions:
1. Check the Laravel logs: `storage/logs/laravel.log`
2. Verify SMTP settings in admin panel
3. Test with manual command execution
4. Check Gmail account security settings

---

**Note**: This system requires Laravel's task scheduler to be running for automated alerts. Set up a cron job:
```bash
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```